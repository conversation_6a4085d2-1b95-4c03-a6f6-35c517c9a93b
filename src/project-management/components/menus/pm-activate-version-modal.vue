<script setup>
const props = defineProps({
  source: {
    type: String,
    validator: value => ['activate_version', 'publish_schedule'].includes(value),
  },
});

const emit = defineEmits(['close', 'confirm']);

const form$ = ref(null);

function onConfirm() {
  emit('confirm', form$.value.data);
}
</script>

<template>
  <HawkModalContainer>
    <Vueform
      ref="form$"
      size="sm"
      :preset="['supress_errors']"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: { container: 12, label: 4, wrapper: 12 },
        sm: { container: 12, label: 4, wrapper: 12 },
        md: { container: 12, label: 4, wrapper: 12 },
      }"
      :endpoint="onConfirm"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ props.source === 'activate_version' ? $t('Activate version') : $t('Publish schedule') }}
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent>
          <div class="flex flex-col">
            <ToggleElement
              v-if="props.source === 'activate_version'"
              name="override_actuals"
              :label="$t('Override actuals')"
              :description="$t('Enable this option to override all recorded actual progress data so far for the activities and reset to the selected version. This operation is irreversible.')"
              class="mb-6"
            />
            <ObjectElement
              name="baseline"
              :label="$t('Create baseline')"
            >
              <ToggleElement
                name="create"
                :description="props.source === 'activate_version' ? $t('Save a snapshot of the current active schedule before activating the new version.') : $t('Save a snapshot of the current schedule.')"
              />
              <TextElement
                :label="$t('Baseline name')"
                :conditions="[['baseline.create', true]]"
                name="name"
                :rules="['required']"
                :columns="{ default: { label: 12, wrapper: 12 }, sm: { label: 12, wrapper: 12 }, md: { label: 12, wrapper: 12 } }"
              />
              <TextareaElement
                :label="$t('Baseline description')"
                :conditions="[['baseline.create', true]]"
                name="description"
                :columns="{ default: { label: 12, wrapper: 12 }, sm: { label: 12, wrapper: 12 }, md: { label: 12, wrapper: 12 } }"
              />
            </ObjectElement>
          </div>
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="emit('close')"
              >
                {{ $t('Cancel') }}
              </ButtonElement>
              <ButtonElement
                name="confirm"
                :submits="true"
              >
                {{ $t('Confirm') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
