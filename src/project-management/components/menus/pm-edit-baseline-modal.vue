<script setup>
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  mode: {
    type: String,
    default: 'create',
    validator: value => ['create', 'edit'].includes(value),
  },
  baseline: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close', 'save']);

const $t = inject('$t');

const project_management_store = useProjectManagementStore();
const { create_baseline, update_baseline } = project_management_store;

const state = reactive({
  form_data: {},
});

async function onSave() {
  if (props.mode === 'create') {
    await create_baseline();
  }
  else if (props.mode === 'edit') {
    const payload = [
      {
        uid: props.baseline.uid,
        name: state.form_data.baseline_name,
        description: state.form_data.baseline_description,
      },
    ];
    await update_baseline(payload);
  }
  emit('save');
}

onMounted(() => {
  if (props.mode === 'edit') {
    state.form_data = {
      baseline_name: props.baseline.name,
      baseline_description: props.baseline.description,
    };
  }
});
</script>

<template>
  <HawkModalContainer>
    <Vueform
      v-model="state.form_data"
      sync
      size="sm"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: {
          container: 12,
          label: 3,
          wrapper: 9,
        },
        sm: {
          label: 4,
        },
        md: {
          label: 4,
        },
        lg: {
          label: 4,
        },
      }"
      :endpoint="onSave"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ props.mode === 'create' ? $t("Create baseline") : $t("Edit baseline") }}
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent>
          <div class="flex flex-col">
            <TextElement
              name="baseline_name"
              :label="$t('Name')"
              class="mb-6"
            />
            <TextareaElement
              name="baseline_description"
              :label="$t('Description')"
            />
          </div>
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="emit('close')"
              >
                {{ $t('Cancel') }}
              </ButtonElement>
              <ButtonElement
                name="save"
                submits
              >
                {{ $t('Save') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
